<template>
  <view class="poster-container">
    <view class="case-detail" v-if="caseInfo">
      <!-- 用户信息 -->
      <view class="user-section">
        <image class="user-avatar" :src="processAvatarUrl(caseInfo.publisher.avatar)" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{ caseInfo.publisher.nickName }}</text>
          <text class="publish-time">{{ formatTime(caseInfo.createTime) }}</text>
        </view>
      </view>

      <!-- 案例标签 -->
      <view class="case-tags" v-if="caseInfo.caseTags">
        <text class="tag" v-for="tag in processTags(caseInfo.caseTags)" :key="tag">#{{ tag }}</text>
      </view>

      <!-- 案例标题 -->
      <view class="case-title">{{ caseInfo.caseTitle }}</view>

      <!-- 案例内容 -->
      <view class="case-content">
        <rich-text :nodes="caseInfo.caseContent"></rich-text>
      </view>

      <!-- 案例图片 -->
      <view class="case-images" v-if="caseInfo.caseImages">
        <view class="image-list">
          <view class="image-item" v-for="(image, index) in processImages(caseInfo.caseImages)" :key="index">
            <image class="case-image" :src="image" mode="widthFix"></image>
          </view>
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-label">{{ caseInfo.templateType }}</text>
          <text class="stats-label">阅读{{ caseInfo.clickCount }}次</text>
        </view>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-title">长按识别二维码查看详情</view>
		<canvas class="qrcode-canvas" id="qrcode" canvas-id="qrcode" style="width: 200px;height: 200px;"></canvas>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-else>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags } from '@/utils/utils.js'
import uqrCode from '@/utils/uqrcode.js'

export default {
  data() {
    return {
      caseId: null,
      caseInfo: null,
      qrcodeSize: 120,
      qrcodeUrl: ''
    }
  },

  onLoad(options) {
    this.caseId = options.caseId
    if (this.caseId) {
      this.loadCaseDetail()
      this.generateQRCodeUrl()
    }
  },

  methods: {
    // 加载案例详情
    async loadCaseDetail() {
      try {
        const res = await caseInfoApi.getCaseDetail(this.caseId)
        this.caseInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: '案例海报'
        })

        // 生成二维码
        this.$nextTick(() => {
          this.generateQRCode()
        })
      } catch (error) {
        console.error('加载案例详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 生成二维码URL
    generateQRCodeUrl() {
      // 构建案例详情页的URL，使用较短的格式以适应二维码容量限制
      // #ifdef H5
      // 使用相对路径，避免完整域名导致URL过长
      this.qrcodeUrl = `http://211.101.244.157:17789/#/pages/case/detail?caseId=${this.caseId}`
      // #endif

      // #ifdef MP-WEIXIN
      this.qrcodeUrl = `pages/case/detail?caseId=${this.caseId}`
      // #endif

      // #ifdef APP-PLUS
      // 使用短域名或相对路径
      this.qrcodeUrl = `case/detail?id=${this.caseId}`
      // #endif

      console.log('生成的二维码URL:', this.qrcodeUrl)
      console.log('二维码URL长度:', this.qrcodeUrl.length)
    },

    // 生成二维码
    generateQRCode() {
      uqrCode.make({
      	canvasId: 'qrcode',
      	componentInstance: this,
      	text: this.qrcodeUrl, // 想生成二维码到内容
      	size: 200,
      	margin: 0,
      	backgroundColor: '#ffffff',
      	foregroundColor: '#000000',
      	fileType: 'jpg',
      	errorCorrectLevel: uqrCode.errorCorrectLevel.H,
      	success: res => {
      		this.imgCode = res // base64的图片格式
      	}
      })
    },

    

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags
  }
}
</script>

<style scoped>
.poster-container {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 0;
  border-radius: 10rpx;
}

.case-detail {
  background-color: #fff;
  padding: 30rpx;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 42rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 36rpx;
  color: #999;
}

/* 案例标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #1890ff;
  font-size: 32rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

/* 案例标题 */
.case-title {
  font-size: 46rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 案例内容 */
.case-content {
  font-size: 36rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 案例图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-item {
  width: 100%;
}

.case-image {
  width: 100%;
  border-radius: 12rpx;
}

/* 统计信息 */
.stats-section {
  margin-bottom: 40rpx;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 34rpx;
  color: #999;
}

/* 二维码区域 */
.qrcode-section {
  text-align: center;
  padding: 40rpx 0;
  border-top: 2rpx solid #f0f0f0;
}

.qrcode-title {
  font-size: 34rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.qrcode-canvas {
  margin: 0 auto;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
